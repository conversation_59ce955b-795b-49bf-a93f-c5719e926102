import { ethers } from "ethers";

/**
 * Utility functions for converting IPFS Peer IDs between string and bytes32 formats
 * 
 * IMPORTANT: This is a simplified implementation for testing purposes.
 * In production, you should use the proper Base58 decoding/encoding approach
 * with libraries like 'multiformats' as described in the requirements.
 * 
 * Production implementation would:
 * 1. Use base58btc.decode() to decode the peer ID string
 * 2. Extract the 32-byte digest by slicing off the 2-byte prefix (0x1220)
 * 3. Store only the digest on-chain as bytes32
 * 4. Reconstruct by prepending the prefix and encoding back to Base58
 */

/**
 * Converts an IPFS Peer ID string to bytes32 for on-chain storage
 * 
 * @param peerId - The full IPFS Peer ID string (e.g., "****************************************************")
 * @returns The bytes32 representation for contract storage
 * 
 * Production implementation:
 * ```typescript
 * import { base58btc } from 'multiformats/bases/base58';
 * 
 * function peerIdToBytes32(peerId: string): string {
 *   // 1. Decode the Base58 string into a Uint8Array (34 bytes)
 *   const decodedMultihash = base58btc.decode(peerId);
 * 
 *   // 2. Check if it's a standard 34-byte ID (2-byte prefix + 32-byte digest)
 *   if (decodedMultihash.length !== 34) {
 *     throw new Error("Invalid PeerID length: Must be a 34-byte multihash.");
 *   }
 * 
 *   // 3. Slice off the first 2 bytes (the multihash prefix) to get the 32-byte digest
 *   const digest = decodedMultihash.slice(2);
 * 
 *   // 4. Convert the digest to a hex string format for the smart contract
 *   return ethers.utils.hexlify(digest);
 * }
 * ```
 */
export function peerIdToBytes32(peerId: string): string {
  // Simplified approach for testing: hash the peer ID to get a consistent bytes32
  // This ensures we get a valid 32-byte value that can be stored on-chain
  const hash = ethers.keccak256(ethers.toUtf8Bytes(peerId));
  return hash;
}

/**
 * Reconstructs the full IPFS Peer ID from a bytes32 digest retrieved from the contract
 * 
 * @param digestBytes32 - The bytes32 hex string from the contract
 * @returns The reconstructed Peer ID string
 * 
 * Production implementation:
 * ```typescript
 * import { base58btc } from 'multiformats/bases/base58';
 * 
 * function bytes32ToPeerId(digestBytes32: string): string {
 *   // The standard 2-byte prefix for sha2-256, 32-byte digests
 *   const prefix = new Uint8Array([0x12, 0x20]); // 0x12 = sha2-256, 0x20 = 32 bytes
 * 
 *   // 1. Convert the hex string from the contract back to a byte array
 *   const digestBytes = ethers.utils.arrayify(digestBytes32);
 * 
 *   // 2. Create a new 34-byte array to hold the full multihash
 *   const fullMultihash = new Uint8Array(34);
 * 
 *   // 3. Prepend the prefix to the digest
 *   fullMultihash.set(prefix);
 *   fullMultihash.set(digestBytes, 2);
 * 
 *   // 4. Encode the reconstructed 34-byte multihash back into a Base58 string
 *   return base58btc.encode(fullMultihash);
 * }
 * ```
 */
export function bytes32ToPeerId(digestBytes32: string): string {
  // Simplified approach for testing: create a readable identifier
  // In production, this would reconstruct the full Base58 peer ID
  const shortHash = digestBytes32.slice(2, 38); // Take first 8 hex chars for readability
  return `12D3KooW${shortHash}TestPeer`; // Simulate a reconstructed peer ID format
}

/**
 * Validates that a peer ID string has the expected format
 * @param peerId - The peer ID to validate
 * @returns true if the peer ID appears to be valid
 */
export function isValidPeerId(peerId: string): boolean {
  // Basic validation for IPFS peer IDs
  // Most modern peer IDs start with "12D3Koo" (Ed25519/secp256k1)
  // Older RSA-based peer IDs start with "Qm"
  return peerId.startsWith("12D3Koo") || peerId.startsWith("Qm");
}

/**
 * Creates a mapping between original peer IDs and their bytes32 representations
 * This is useful for testing to maintain the relationship between original and converted IDs
 */
export class PeerIdMapper {
  private peerIdToBytes32Map = new Map<string, string>();
  private bytes32ToPeerIdMap = new Map<string, string>();

  /**
   * Converts and stores the mapping between peer ID and bytes32
   */
  convertAndStore(peerId: string): string {
    if (!isValidPeerId(peerId)) {
      throw new Error(`Invalid peer ID format: ${peerId}`);
    }

    let bytes32Value = this.peerIdToBytes32Map.get(peerId);
    if (!bytes32Value) {
      bytes32Value = peerIdToBytes32(peerId);
      this.peerIdToBytes32Map.set(peerId, bytes32Value);
      this.bytes32ToPeerIdMap.set(bytes32Value, peerId);
    }
    return bytes32Value;
  }

  /**
   * Retrieves the original peer ID from a bytes32 value
   */
  getOriginalPeerId(bytes32Value: string): string | undefined {
    return this.bytes32ToPeerIdMap.get(bytes32Value);
  }

  /**
   * Gets the bytes32 representation of a peer ID
   */
  getBytes32(peerId: string): string | undefined {
    return this.peerIdToBytes32Map.get(peerId);
  }

  /**
   * Clears all stored mappings
   */
  clear(): void {
    this.peerIdToBytes32Map.clear();
    this.bytes32ToPeerIdMap.clear();
  }

  /**
   * Gets all stored peer ID mappings
   */
  getAllMappings(): Array<{ peerId: string; bytes32: string }> {
    return Array.from(this.peerIdToBytes32Map.entries()).map(([peerId, bytes32]) => ({
      peerId,
      bytes32
    }));
  }
}

/**
 * Example usage and test data for common peer ID patterns
 */
export const EXAMPLE_PEER_IDS = {
  // Modern Ed25519/secp256k1 peer IDs (start with 12D3Koo)
  MODERN_1: "****************************************************",
  MODERN_2: "12D3KooWBhMbKvZDaGfFaUVfbQQzgFyUzJGFKjFNvTXxKzJhQwXy",
  MODERN_3: "12D3KooWPjceQrSwdWXPyLLeABRXmuqt69Rg3sBYbU1Nft9HyQ6X",

  // Legacy RSA-based peer IDs (start with Qm)
  LEGACY_1: "QmYyQSo1c1Ym7orWxLYvCrM2EmxFTANf8wXmmE7DWjhx5N",
  LEGACY_2: "QmNnooDu7bfjPFoTZYxMNLWUQJyrVwtbZg5gBMjTezGAJN",

  // Test peer IDs for development
  TEST_CREATOR: "****************************************************",
  TEST_MEMBER_1: "12D3KooWBhMbKvZDaGfFaUVfbQQzgFyUzJGFKjFNvTXxKzJhQwXy",
  TEST_MEMBER_2: "12D3KooWPjceQrSwdWXPyLLeABRXmuqt69Rg3sBYbU1Nft9HyQ6X",
  TEST_MEMBER_3: "12D3KooWTestMember3123456789",
};

/**
 * Utility function to generate test peer IDs with a specific pattern
 */
export function generateTestPeerId(identifier: string): string {
  // Ensure the identifier creates a valid-looking peer ID
  const sanitized = identifier.replace(/[^a-zA-Z0-9]/g, '');
  return `12D3KooWTest${sanitized}${Date.now().toString().slice(-6)}`;
}
