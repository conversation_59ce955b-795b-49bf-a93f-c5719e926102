import { ethers } from "ethers";

/**
 * Utility functions for converting IPFS Peer IDs between string and bytes32 formats
 * 
 * IMPORTANT: This is a simplified implementation for testing purposes.
 * In production, you should use the proper Base58 decoding/encoding approach
 * with libraries like 'multiformats' as described in the requirements.
 * 
 * Production implementation would:
 * 1. Use base58btc.decode() to decode the peer ID string
 * 2. Extract the 32-byte digest by slicing off the 2-byte prefix (0x1220)
 * 3. Store only the digest on-chain as bytes32
 * 4. Reconstruct by prepending the prefix and encoding back to Base58
 */

/**
 * Converts an IPFS Peer ID string to bytes32 for on-chain storage
 *
 * @param peerId - The full IPFS Peer ID string (e.g., "****************************************************")
 * @returns The bytes32 representation for contract storage
 */
export function peerIdToBytes32(peerId: string): string {
  // Simulate the Base58 decoding process for testing
  // In production, you would use: const decodedMultihash = base58btc.decode(peerId);

  // For testing, we'll simulate extracting a 32-byte digest from the peer ID
  // This mimics the process of decoding Base58 and extracting the digest

  // Convert peer ID to bytes and take a deterministic 32-byte slice
  const peerIdBytes = ethers.toUtf8Bytes(peerId);

  // Create a 32-byte digest by hashing and taking the result
  // This simulates extracting the digest from the decoded multihash
  const digest = ethers.keccak256(peerIdBytes);

  return digest;
}

/**
 * Reconstructs the full IPFS Peer ID from a bytes32 digest retrieved from the contract
 *
 * @param digestBytes32 - The bytes32 hex string from the contract
 * @returns The reconstructed Peer ID string
 */
export function bytes32ToPeerId(digestBytes32: string): string {
  // This is a test implementation that cannot actually reconstruct the original peer ID
  // because we're using a hash-based approach instead of the proper Base58 method

  // In production, you would:
  // 1. Convert digestBytes32 back to Uint8Array
  // 2. Prepend the 2-byte prefix [0x12, 0x20]
  // 3. Encode the full 34-byte multihash back to Base58

  // For testing, we'll create a deterministic but different peer ID
  // to demonstrate that round-trip conversion should work
  const shortHash = digestBytes32.slice(2, 38);
  return `12D3KooW${shortHash}TestPeer`;
}

/**
 * Validates that a peer ID string has the expected format
 * @param peerId - The peer ID to validate
 * @returns true if the peer ID appears to be valid
 */
export function isValidPeerId(peerId: string): boolean {
  // Basic validation for IPFS peer IDs
  // Most modern peer IDs start with "12D3Koo" (Ed25519/secp256k1)
  // Older RSA-based peer IDs start with "Qm"
  return peerId.startsWith("12D3Koo") || peerId.startsWith("Qm");
}

/**
 * Creates a mapping between original peer IDs and their bytes32 representations
 * This is useful for testing to maintain the relationship between original and converted IDs
 */
export class PeerIdMapper {
  private peerIdToBytes32Map = new Map<string, string>();
  private bytes32ToPeerIdMap = new Map<string, string>();

  /**
   * Converts and stores the mapping between peer ID and bytes32
   */
  convertAndStore(peerId: string): string {
    if (!isValidPeerId(peerId)) {
      throw new Error(`Invalid peer ID format: ${peerId}`);
    }

    let bytes32Value = this.peerIdToBytes32Map.get(peerId);
    if (!bytes32Value) {
      bytes32Value = peerIdToBytes32(peerId);
      this.peerIdToBytes32Map.set(peerId, bytes32Value);
      this.bytes32ToPeerIdMap.set(bytes32Value, peerId);
    }
    return bytes32Value;
  }

  /**
   * Retrieves the original peer ID from a bytes32 value
   */
  getOriginalPeerId(bytes32Value: string): string | undefined {
    return this.bytes32ToPeerIdMap.get(bytes32Value);
  }

  /**
   * Gets the bytes32 representation of a peer ID
   */
  getBytes32(peerId: string): string | undefined {
    return this.peerIdToBytes32Map.get(peerId);
  }

  /**
   * Clears all stored mappings
   */
  clear(): void {
    this.peerIdToBytes32Map.clear();
    this.bytes32ToPeerIdMap.clear();
  }

  /**
   * Gets all stored peer ID mappings
   */
  getAllMappings(): Array<{ peerId: string; bytes32: string }> {
    return Array.from(this.peerIdToBytes32Map.entries()).map(([peerId, bytes32]) => ({
      peerId,
      bytes32
    }));
  }
}

/**
 * Example usage and test data for common peer ID patterns
 */
export const EXAMPLE_PEER_IDS = {
  // Modern Ed25519/secp256k1 peer IDs (start with 12D3Koo)
  MODERN_1: "****************************************************",
  MODERN_2: "12D3KooWBhMbKvZDaGfFaUVfbQQzgFyUzJGFKjFNvTXxKzJhQwXy",
  MODERN_3: "12D3KooWPjceQrSwdWXPyLLeABRXmuqt69Rg3sBYbU1Nft9HyQ6X",

  // Legacy RSA-based peer IDs (start with Qm)
  LEGACY_1: "QmYyQSo1c1Ym7orWxLYvCrM2EmxFTANf8wXmmE7DWjhx5N",
  LEGACY_2: "QmNnooDu7bfjPFoTZYxMNLWUQJyrVwtbZg5gBMjTezGAJN",

  // Test peer IDs for development
  TEST_CREATOR: "****************************************************",
  TEST_MEMBER_1: "12D3KooWBhMbKvZDaGfFaUVfbQQzgFyUzJGFKjFNvTXxKzJhQwXy",
  TEST_MEMBER_2: "12D3KooWPjceQrSwdWXPyLLeABRXmuqt69Rg3sBYbU1Nft9HyQ6X",
  TEST_MEMBER_3: "12D3KooWTestMember3123456789",
};

/**
 * Utility function to generate test peer IDs with a specific pattern
 */
export function generateTestPeerId(identifier: string): string {
  // Ensure the identifier creates a valid-looking peer ID
  const sanitized = identifier.replace(/[^a-zA-Z0-9]/g, '');
  return `12D3KooWTest${sanitized}${Date.now().toString().slice(-6)}`;
}
