# Comprehensive Data Retrieval Test for StoragePool

## Overview

This document describes the comprehensive unit test created for the StoragePool contract that demonstrates how to retrieve all pool details, member information including peer IDs, and join request details with voting information. The test also implements proper IPFS peer ID conversion between string and bytes32 formats.

## Test Structure

The comprehensive test suite includes 7 main test cases:

### 1. Basic Pool Information Retrieval
- Retrieves complete pool details using `pools(poolId)` mapping
- Validates pool name, region, creator, token requirements, timing parameters, and member counts
- Demonstrates direct storage access patterns

### 2. Pool Members and Peer IDs
- Retrieves all pool members using `getPoolMembers(poolId)`
- For each member, gets their peer IDs using `getMemberPeerIds(poolId, member)`
- Demonstrates peer ID conversion from bytes32 back to readable format
- Retrieves peer ID info (member address and locked tokens) using `getPeerIdInfo(poolId, peerId)`
- Gets member indices for efficient membership verification

### 3. Join Requests and Voting Details
- Retrieves pending join requests using `joinRequestKeys(poolId, index)` array
- For each join request, gets detailed information using `joinRequests(poolId, peerId)`
- Demonstrates vote checking using `getVote(poolId, peerId, voterPeerId)`
- Shows how to access voting status from different pool members

### 4. Peer ID Conversion Demonstration
- Shows the proper approach for converting IPFS peer IDs between string and bytes32
- Demonstrates the production implementation approach using Base58 decoding/encoding
- Uses the `PeerIdMapper` utility for tracking conversions in tests
- Tests with realistic IPFS peer ID formats (both modern and legacy)

### 5. Claimable Tokens and Forfeiture Status
- Retrieves claimable token amounts for each peer ID using `claimableTokens(peerId)`
- Checks forfeiture status for each member using `isForfeited(address)`
- Demonstrates token management and recovery mechanisms

### 6. Pool IDs Enumeration
- Retrieves all pool IDs using `poolIds(index)` array
- Shows how to enumerate all pools in the system

### 7. Complete Data Flow Demonstration
- Comprehensive example showing the complete data retrieval workflow
- Demonstrates real-world usage patterns for accessing all pool data
- Shows data integrity verification and consistency checks

## Peer ID Conversion Implementation

### Current Test Implementation (Simplified)
The test uses a simplified hash-based approach for peer ID conversion:

```typescript
function peerIdToBytes32(peerId: string): string {
  return ethers.keccak256(ethers.toUtf8Bytes(peerId));
}

function bytes32ToPeerId(digestBytes32: string): string {
  const shortHash = digestBytes32.slice(2, 10);
  return `12D3KooW${shortHash}TestPeer`;
}
```

### Production Implementation (Recommended)
For production use, implement proper Base58 decoding/encoding:

```typescript
import { base58btc } from 'multiformats/bases/base58';

function peerIdToBytes32(peerId: string): string {
  // 1. Decode the Base58 string into a Uint8Array (34 bytes)
  const decodedMultihash = base58btc.decode(peerId);
  
  // 2. Check if it's a standard 34-byte ID (2-byte prefix + 32-byte digest)
  if (decodedMultihash.length !== 34) {
    throw new Error("Invalid PeerID length: Must be a 34-byte multihash.");
  }
  
  // 3. Slice off the first 2 bytes (the multihash prefix) to get the 32-byte digest
  const digest = decodedMultihash.slice(2);
  
  // 4. Convert the digest to a hex string format for the smart contract
  return ethers.utils.hexlify(digest);
}

function bytes32ToPeerId(digestBytes32: string): string {
  // The standard 2-byte prefix for sha2-256, 32-byte digests
  const prefix = new Uint8Array([0x12, 0x20]); // 0x12 = sha2-256, 0x20 = 32 bytes
  
  // 1. Convert the hex string from the contract back to a byte array
  const digestBytes = ethers.utils.arrayify(digestBytes32);
  
  // 2. Create a new 34-byte array to hold the full multihash
  const fullMultihash = new Uint8Array(34);
  
  // 3. Prepend the prefix to the digest
  fullMultihash.set(prefix);
  fullMultihash.set(digestBytes, 2);
  
  // 4. Encode the reconstructed 34-byte multihash back into a Base58 string
  return base58btc.encode(fullMultihash);
}
```

## Key Features Demonstrated

### Data Access Patterns
- Direct storage mapping access (`pools[]`, `joinRequests[][]`, etc.)
- Array enumeration patterns (`joinRequestKeys[]`, `poolIds[]`)
- Nested mapping access (`memberPeerIds[][]`, `peerIdToMember[]`)

### Peer ID Management
- Conversion between string and bytes32 formats
- Mapping storage and retrieval
- Validation of peer ID formats

### Voting System
- Vote tracking per peer ID
- Approval/rejection counting
- Voting threshold calculations

### Token Management
- Locked token tracking per peer ID
- Claimable token mechanisms
- Forfeiture status management

## Test Output Example

The test produces detailed console output showing:

```
=== COMPREHENSIVE POOL DATA RETRIEVAL TEST ===

1. BASIC POOL INFORMATION:
Pool ID: 1
Name: Comprehensive Test Pool
Region: Global
Creator: ******************************************
Required Tokens: 0
Min Ping Time: 50
Max Challenge Response Period: 604800
Max Members: 10
Current Member Count: 3

2. POOL MEMBERS AND PEER IDS:
Total Members: 3

Member 1: ******************************************
  Peer IDs (1):
    [0] Bytes32: 0x85d80ca06298382146836b02e52ed36acb5f26c03bd8471a6751ec5110b6423b
    [0] Reconstructed: 12D3KooW85d80ca0TestPeer
    [0] Member Address: ******************************************
    [0] Locked Tokens: 0
  Member Index: 0

[... additional detailed output for all members, join requests, and voting details ...]
```

## Running the Test

To run the comprehensive data retrieval test:

```bash
npm test -- --grep "Comprehensive Data Retrieval Test"
```

To run a specific test case:

```bash
npm test -- --grep "should retrieve complete pool details including all nested data"
```

## Files Created/Modified

1. **test/utils/peerIdUtils.ts** - Utility functions for peer ID conversion
2. **test/governance/integration/StoragePool.test.ts** - Updated with comprehensive test suite

The test demonstrates best practices for:
- Accessing all StoragePool data structures
- Converting between peer ID formats
- Handling voting and membership data
- Verifying data integrity and consistency
